<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渐变画笔彻底修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        canvas {
            border: 2px solid #ddd;
            border-radius: 4px;
            cursor: crosshair;
            background: white;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            color: #155724;
        }
        .info {
            margin-top: 15px;
            padding: 10px;
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .test-steps {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .fix-highlight {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 渐变画笔彻底修复测试</h1>
        
        <div class="fix-highlight">
            <h3>🔧 彻底修复方案：</h3>
            <p><strong>核心思路：</strong>将每个绘制点的具体颜色值直接保存到历史记录中，而不是依赖颜色计算函数。</p>
            <ul>
                <li><strong>startLineWithColor:</strong> 保存起始点的具体颜色值</li>
                <li><strong>goLineWithColor:</strong> 保存每个绘制点的具体颜色值</li>
                <li><strong>历史重放:</strong> 使用保存的颜色值，不再重新计算</li>
                <li><strong>结果:</strong> 撤销后颜色完全不变，因为使用的是固定的颜色值</li>
            </ul>
        </div>
        
        <div class="test-steps">
            <h3>测试步骤：</h3>
            <ol>
                <li>点击"绘制测试线条"按钮，绘制几条不同的渐变线</li>
                <li>观察每条线的颜色渐变效果</li>
                <li>点击"撤销"按钮多次，观察已绘制线条的颜色</li>
                <li>点击"重做"按钮，验证颜色恢复</li>
                <li>重复撤销/重做操作，验证颜色稳定性</li>
            </ol>
            <p><strong>预期结果：</strong>无论如何撤销和重做，已完成的线条颜色都应该保持完全不变。</p>
        </div>
        
        <div class="controls">
            <button class="btn-primary" onclick="drawTestLine1()">绘制测试线条1</button>
            <button class="btn-primary" onclick="drawTestLine2()">绘制测试线条2</button>
            <button class="btn-primary" onclick="drawTestLine3()">绘制测试线条3</button>
            <button class="btn-warning" onclick="drawRandomLine()">绘制随机线条</button>
            <br>
            <button class="btn-danger" onclick="simulateUndo()">撤销</button>
            <button class="btn-success" onclick="simulateRedo()">重做</button>
            <button class="btn-secondary" onclick="clearCanvas()">清空画布</button>
            <button class="btn-secondary" onclick="showHistory()">显示历史记录</button>
        </div>

        <canvas id="testCanvas" width="800" height="400"></canvas>
        
        <div class="status" id="status">
            <strong>状态:</strong> 准备测试彻底修复方案 - 颜色值直接保存到历史记录
        </div>
        
        <div class="info">
            <h4>修复技术细节：</h4>
            <ul>
                <li><strong>问题根源：</strong>撤销时重新计算颜色，受时间和随机因素影响</li>
                <li><strong>解决方案：</strong>直接保存每个点的RGB颜色值到历史记录</li>
                <li><strong>startLineWithColor(x, y, pressure, color)：</strong>保存起始点颜色</li>
                <li><strong>goLineWithColor(x, y, pressure, color)：</strong>保存每个绘制点颜色</li>
                <li><strong>历史重放：</strong>使用保存的固定颜色值，不再重新计算</li>
                <li><strong>优势：</strong>100%确保颜色不变，不受任何外部因素影响</li>
            </ul>
        </div>
        
        <div id="historyDisplay" style="margin-top: 15px; display: none;">
            <h4>历史记录详情：</h4>
            <pre id="historyContent" style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px;"></pre>
        </div>
    </div>

    <script>
        // 模拟彻底修复后的渐变画笔测试
        const canvas = document.getElementById('testCanvas');
        const ctx = canvas.getContext('2d');
        const statusEl = document.getElementById('status');
        
        // 模拟画笔状态
        let globalColorProgress = 0;
        let strokeHistory = []; // 保存每个笔画的完整历史记录（包括颜色）
        let currentHistoryIndex = -1;
        
        // 颜色调色板
        const colorPalette = [
            { r: 255, g: 0, b: 0 },     // 红
            { r: 255, g: 127, b: 0 },   // 橙红
            { r: 255, g: 200, b: 0 },   // 橙黄
            { r: 200, g: 255, b: 0 },   // 黄绿
            { r: 0, g: 255, b: 100 },   // 绿
            { r: 0, g: 200, b: 255 },   // 青绿
            { r: 0, g: 100, b: 255 },   // 蓝
            { r: 100, g: 0, b: 255 },   // 蓝紫
            { r: 200, g: 0, b: 200 },   // 紫
            { r: 255, g: 0, b: 100 }    // 紫红
        ];
        
        // 平滑步进函数
        function smoothStep(t) {
            return t * t * (3 - 2 * t);
        }

        // 计算颜色（仅在绘制时使用，重放时不使用）
        function calculateColor(progress) {
            const smoothRandom = Math.sin(Date.now() * 0.001) * 0.3 * 0.1;
            const adjustedProgress = Math.max(0, Math.min(1, progress + smoothRandom));
            const smoothProgress = smoothStep(adjustedProgress);

            const position = smoothProgress * (colorPalette.length - 1);
            const index1 = Math.floor(position);
            const index2 = Math.min(index1 + 1, colorPalette.length - 1);
            const t = position - index1;

            const color1 = colorPalette[index1];
            const color2 = colorPalette[index2];
            const smoothT = smoothStep(t);

            return {
                r: Math.round(color1.r + (color2.r - color1.r) * smoothT),
                g: Math.round(color1.g + (color2.g - color1.g) * smoothT),
                b: Math.round(color1.b + (color2.b - color1.b) * smoothT)
            };
        }
        
        // 绘制渐变线段
        function drawGradientSegment(p1, p2) {
            const gradient = ctx.createLinearGradient(p1.x, p1.y, p2.x, p2.y);
            gradient.addColorStop(0, `rgb(${p1.color.r}, ${p1.color.g}, ${p1.color.b})`);
            gradient.addColorStop(1, `rgb(${p2.color.r}, ${p2.color.g}, ${p2.color.b})`);
            
            ctx.strokeStyle = gradient;
            ctx.lineWidth = 15;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            ctx.beginPath();
            ctx.moveTo(p1.x, p1.y);
            ctx.lineTo(p2.x, p2.y);
            ctx.stroke();
        }
        
        // 绘制测试线条（模拟彻底修复后的行为）
        function drawTestLine(startX, startY, endX, endY, lineNumber) {
            // 为当前笔画设置独立的颜色进度起点
            const strokeColorProgress = globalColorProgress;
            
            // 创建笔画历史记录（关键：保存每个点的具体颜色值）
            const strokeRecord = {
                type: 'stroke',
                startX, startY, endX, endY,
                actions: [] // 保存具体的绘制动作和颜色值
            };
            
            const steps = 50;
            let currentStrokeProgress = strokeColorProgress;
            
            // 起始点
            const startColor = calculateColor(currentStrokeProgress);
            const startPoint = { x: startX, y: startY, color: startColor };
            
            // 保存起始动作和颜色（关键修复点）
            strokeRecord.actions.push({
                action: 'startLineWithColor',
                x: startX, y: startY, pressure: 1.0, color: startColor
            });
            
            for (let i = 1; i <= steps; i++) {
                const x = startX + (endX - startX) * (i / steps);
                const y = startY + (endY - startY) * (i / steps);
                
                // 更新当前笔画的颜色进度
                currentStrokeProgress += 0.5 * 0.01;
                if (currentStrokeProgress > 1) {
                    currentStrokeProgress = 0;
                }
                
                const color = calculateColor(currentStrokeProgress);
                const point = { x, y, color };
                
                // 保存每个绘制动作和具体颜色值（关键修复点）
                strokeRecord.actions.push({
                    action: 'goLineWithColor',
                    x: x, y: y, pressure: 1.0, color: color
                });
                
                // 绘制线段
                const prevPoint = i === 1 ? startPoint : 
                    { x: startX + (endX - startX) * ((i-1) / steps), 
                      y: startY + (endY - startY) * ((i-1) / steps), 
                      color: strokeRecord.actions[i-1].color };
                drawGradientSegment(prevPoint, point);
            }
            
            // 结束动作
            strokeRecord.actions.push({
                action: 'endLine',
                x: endX, y: endY
            });
            
            // 笔画结束时，将当前笔画的颜色进度同步到全局进度
            globalColorProgress = currentStrokeProgress;
            
            // 保存到历史记录
            strokeHistory = strokeHistory.slice(0, currentHistoryIndex + 1);
            strokeHistory.push(strokeRecord);
            currentHistoryIndex = strokeHistory.length - 1;
            
            statusEl.innerHTML = `<strong>状态:</strong> 绘制完成线条${lineNumber}，保存了${strokeRecord.actions.length}个动作到历史记录`;
        }
        
        function drawTestLine1() {
            drawTestLine(50, 100, 350, 150, 1);
        }
        
        function drawTestLine2() {
            drawTestLine(100, 200, 400, 250, 2);
        }
        
        function drawTestLine3() {
            drawTestLine(150, 300, 450, 350, 3);
        }
        
        function drawRandomLine() {
            const startX = Math.random() * 300 + 50;
            const startY = Math.random() * 200 + 100;
            const endX = startX + Math.random() * 300 + 100;
            const endY = startY + (Math.random() - 0.5) * 100;
            drawTestLine(startX, startY, endX, endY, 'R');
        }
        
        // 模拟撤销（使用保存的颜色值重新绘制）
        function simulateUndo() {
            if (currentHistoryIndex < 0) {
                statusEl.innerHTML = '<strong>状态:</strong> 无法撤销，没有更多历史记录';
                return;
            }
            
            currentHistoryIndex--;
            redrawFromHistory();
            statusEl.innerHTML = `<strong>状态:</strong> 撤销完成，当前历史索引: ${currentHistoryIndex}`;
        }
        
        // 模拟重做
        function simulateRedo() {
            if (currentHistoryIndex >= strokeHistory.length - 1) {
                statusEl.innerHTML = '<strong>状态:</strong> 无法重做，已经是最新状态';
                return;
            }
            
            currentHistoryIndex++;
            redrawFromHistory();
            statusEl.innerHTML = `<strong>状态:</strong> 重做完成，当前历史索引: ${currentHistoryIndex}`;
        }
        
        // 从历史记录重新绘制（使用保存的颜色值）
        function redrawFromHistory() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 重新绘制所有历史记录中的笔画（关键：使用保存的颜色值）
            for (let i = 0; i <= currentHistoryIndex; i++) {
                const stroke = strokeHistory[i];
                
                let currentPoint = null;
                let prevPoint = null;
                
                // 重放每个动作，使用保存的颜色值
                for (let j = 0; j < stroke.actions.length; j++) {
                    const action = stroke.actions[j];
                    
                    if (action.action === 'startLineWithColor') {
                        currentPoint = { x: action.x, y: action.y, color: action.color };
                        prevPoint = currentPoint;
                    } else if (action.action === 'goLineWithColor') {
                        currentPoint = { x: action.x, y: action.y, color: action.color };
                        if (prevPoint) {
                            // 使用保存的固定颜色值绘制（关键修复点）
                            drawGradientSegment(prevPoint, currentPoint);
                        }
                        prevPoint = currentPoint;
                    }
                }
            }
        }
        
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            strokeHistory = [];
            currentHistoryIndex = -1;
            globalColorProgress = 0;
            statusEl.innerHTML = '<strong>状态:</strong> 画布已清空，历史记录已重置';
        }
        
        function showHistory() {
            const historyDiv = document.getElementById('historyDisplay');
            const historyContent = document.getElementById('historyContent');
            
            if (historyDiv.style.display === 'none') {
                let historyText = `历史记录总数: ${strokeHistory.length}\n当前索引: ${currentHistoryIndex}\n\n`;
                
                strokeHistory.forEach((stroke, index) => {
                    historyText += `笔画 ${index + 1}:\n`;
                    historyText += `  动作数量: ${stroke.actions.length}\n`;
                    stroke.actions.forEach((action, actionIndex) => {
                        if (action.action === 'startLineWithColor' || action.action === 'goLineWithColor') {
                            historyText += `  ${actionIndex + 1}. ${action.action}: (${action.x.toFixed(1)}, ${action.y.toFixed(1)}) 颜色: rgb(${action.color.r}, ${action.color.g}, ${action.color.b})\n`;
                        } else {
                            historyText += `  ${actionIndex + 1}. ${action.action}\n`;
                        }
                    });
                    historyText += '\n';
                });
                
                historyContent.textContent = historyText;
                historyDiv.style.display = 'block';
                statusEl.innerHTML = '<strong>状态:</strong> 历史记录已显示 - 可以看到每个点的具体颜色值';
            } else {
                historyDiv.style.display = 'none';
                statusEl.innerHTML = '<strong>状态:</strong> 历史记录已隐藏';
            }
        }
        
        // 初始化
        statusEl.innerHTML = '<strong>状态:</strong> 彻底修复方案测试环境已准备就绪 - 颜色值直接保存到历史记录';
    </script>
</body>
</html>
