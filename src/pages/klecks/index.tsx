import Klecks from '@/utils/klecks/embed'

import { debounce, hexToRgb } from '@/utils/tool'
import { useEffect, useState, useRef } from 'react'

export default function Index() {
  const klecksRef = useRef<Klecks>()
  const [color, setColor] = useState<string>('#13A8A8')
  useEffect(() => {
    initKlecks()
  }, [])
  // 初始化画板
  const initKlecks = () => {
    const klecks: Klecks = new Klecks({
      // disableAutoFit: true,
      embedUrl: '',
      onSubmit: (onSuccess) => {
        setTimeout(() => {
          onSuccess()
          location.reload()
        }, 500)
      }
    })

    klecksRef.current = klecks
    const h = 800
    const w = 800
    klecks.openProject({
      width: w,
      height: h,
      layers: [
        {
          name: 'Background',
          isVisible: true,
          opacity: 1,
          mixModeStr: 'source-over',
          image: (() => {
            const canvas = document.createElement('canvas') as HTMLCanvasElement
            canvas.width = w
            canvas.height = h
            const ctx = canvas.getContext('2d') as CanvasRenderingContext2D
            ctx.save()
            ctx.fillStyle = '#ffffff'
            ctx.fillRect(0, 0, canvas.width, canvas.height)
            ctx.restore()
            return canvas
          })()
        }
      ]
    })
    setTimeout(() => {
      klecks?.instance?.klApp?.currentBrushUi.setSize(20)
      console.log('🚀 ~ initKlecks ~ klecks?.instance?.klApp:', klecks?.instance?.klApp)
    }, 600)
  }

  const changeColor = () => {
    const rgb = hexToRgb(color)
    klecksRef.current?.instance?.klApp?.klColorSlider.setColor2({
      r: rgb[0],
      g: rgb[1],
      b: rgb[2]
    })
  }

  const klAPP = () => klecksRef.current?.instance?.klApp
  return (
    <div>
      <div className="absolute top-0 left-0 flex  justify-between items-center w-full h-[80px] bg-[#fff] z-[11]">
        <div
          onClick={() => {
            klecksRef.current?.instance?.klApp?.toolspaceToolRow.onActivate('brush')
            klecksRef.current?.instance?.klApp?.toolspaceToolRow?.setCurrentBrush('penBrush')
            klecksRef.current?.instance?.klApp?.currentBrushUi.setAlpha(0)
          }}
        >
          画笔
        </div>
        <div
          onClick={() => {
            klecksRef.current?.instance?.klApp?.toolspaceToolRow?.setCurrentBrush('penBrush')
            klecksRef.current?.instance?.klApp?.currentBrushUi.setAlpha(1)
          }}
        >
          粉笔
        </div>
        <div
          onClick={() => {
            klecksRef.current?.instance?.klApp?.toolspaceToolRow?.setCurrentBrush('penBrush')
            klecksRef.current?.instance?.klApp?.currentBrushUi.setAlpha(2)
          }}
        >
          立体笔
        </div>
        <div
          onClick={() => {
            klecksRef.current?.instance?.klApp?.toolspaceToolRow?.setCurrentBrush('penBrush')
            klecksRef.current?.instance?.klApp?.currentBrushUi.setAlpha(3)
          }}
        >
          方块笔
        </div>
        <div
          onClick={() => {
            klecksRef.current?.instance?.klApp?.toolspaceToolRow.onActivate('brush')
            klecksRef.current?.instance?.klApp?.toolspaceToolRow?.setCurrentBrush('calligraphyBrush')
            // klecksRef.current?.instance?.klApp?.currentBrushUi.setAlpha(4)
          }}
        >
          书法笔
        </div>

        <div
          onClick={() => {
            klecksRef.current?.instance?.klApp?.toolspaceToolRow.onActivate('brush')
            klecksRef.current?.instance?.klApp?.toolspaceToolRow?.setCurrentBrush('gradientBrush')
            // klecksRef.current?.instance?.klApp?.currentBrushUi.setAlpha(4)
          }}
        >
          渐变笔
        </div>
        <a
          onClick={() => {
            console.log(klecksRef.current)
            klecksRef.current?.instance?.klApp?.toolspaceToolRow.onActivate('brush')
            klecksRef.current?.instance?.klApp?.toolspaceToolRow?.setCurrentBrush('blendBrush')
          }}
        >
          水彩笔
        </a>
        <a
          onClick={() => {
            klecksRef.current?.instance?.klApp?.toolspaceToolRow.onActivate('brush')

            klecksRef.current?.instance?.klApp?.toolspaceToolRow?.setCurrentBrush('eraserBrush')
          }}
        >
          橡皮
        </a>
        <a
          onClick={() => {
            klecksRef.current?.instance?.klApp?.toolspaceToolRow.onActivate('paintBucket')
          }}
        >
          油漆桶
        </a>
        <a
          onClick={() => {
            klecksRef.current?.instance?.klApp?.easel?.onUndo!(false)
          }}
        >
          撤销
        </a>
        <a
          onClick={() => {
            klecksRef.current?.instance?.klApp?.easel?.onRedo!(false)
          }}
        >
          前进
        </a>
        <input
          type="range"
          defaultValue={20}
          onChange={(e) => {
            const value = e.target.value
            klecksRef.current?.instance?.klApp?.currentBrushUi.setSize(value)
          }}
        />
      </div>

      <div id="loading-screen" className="text-[#eee] text-center w-screen text-[25px] mt-[100px]">
        Loading Klecks
      </div>
      <div id="klecks-root" className="absolute w-full h-full z-10"></div>
    </div>
  )
}
