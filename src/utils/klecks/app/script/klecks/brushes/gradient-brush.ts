import { BB } from '../../bb/bb'
import { IRGB, TPressureInput } from '../kl-types'
import { IHistoryEntry, KlHistory, THistoryInnerActions } from '../history/kl-history'

export interface IGradientBrushHistoryEntry extends IHistoryEntry {
  tool: ['brush', 'GradientBrush']
  actions: THistoryInnerActions<GradientBrush>[]
}

interface IGradientPoint {
  x: number
  y: number
  pressure: number
  color: IRGB
  size: number
  opacity: number
}

/**
 * 渐变色画笔 - 实现边画边颜色随机变化的渐变效果
 * 支持多种渐变模式和颜色配置
 */
export class GradientBrush {
  private context: CanvasRenderingContext2D = {} as CanvasRenderingContext2D
  private history: KlHistory | undefined
  private historyEntry: IGradientBrushHistoryEntry | undefined

  // 基本设置
  private settingSize: number = 20
  private settingOpacity: number = 1
  private settingBaseColor: IRGB = { r: 255, g: 0, b: 0 } // 基础颜色
  private settingLockLayerAlpha: boolean = false
  private settingHasSizePressure: boolean = true
  private settingHasOpacityPressure: boolean = false

  // 渐变效果参数
  private gradientMode: 'rainbow' | 'warm' | 'cool' | 'custom' = 'rainbow'
  private colorChangeSpeed: number = 0.4 // 颜色变化速度 (0-1) - 降低默认速度
  private colorRandomness: number = 0.4 // 颜色随机性 (0-1) - 适中的随机性
  private gradientSmoothing: number = 0.7 // 渐变平滑度 (0-1)
  private colorTransitionSmoothing: number = 0.8 // 颜色过渡丝滑度 (0-1)
  private antiColorBanding: number = 0.8 // 抗色差强度 (0-1)
  
  // 自定义渐变颜色
  private customColors: IRGB[] = [
    { r: 255, g: 0, b: 0 },   // 红
    { r: 255, g: 165, b: 0 }, // 橙
    { r: 255, g: 255, b: 0 }, // 黄
    { r: 0, g: 255, b: 0 },   // 绿
    { r: 0, g: 0, b: 255 },   // 蓝
    { r: 128, g: 0, b: 128 }  // 紫
  ]

  // 绘制状态
  private strokePoints: IGradientPoint[] = []
  private colorProgress: number = 0
  private inputArr: TPressureInput[] = []
  private inputIsDrawing: boolean = false
  private isDrawingState: boolean = false // 内部绘制状态

  constructor() {
    console.log(`[GradientBrush] Constructor called`)
  }

  /**
   * 重置绘制状态
   */
  resetState(): void {
    this.strokePoints = []
    this.colorProgress = 0
    this.isDrawingState = false
  }



  /**
   * 计算当前应该使用的颜色（降低频率版本 - 更平缓的颜色变化）
   */
  private calculateCurrentColor(progress: number): IRGB {
    // 降低时间基础随机性的频率
    const timeBasedRandom = Math.sin(Date.now() * 0.001) * 0.5 + 0.5
    const progressBasedRandom = Math.sin(progress * Math.PI * 2) * 0.5 + 0.5

    // 降低随机性强度，让颜色变化更平缓
    const randomFactor = (timeBasedRandom + progressBasedRandom) * 0.3
    const moderateRandomness = this.colorRandomness * 1.5 // 适中的随机性

    // 生成更平缓的色相、饱和度和亮度变化
    const hue = (progress + randomFactor * moderateRandomness) * 360
    const saturation = 0.85 + Math.sin(progress * Math.PI * 1.5) * 0.15 // 70%-100%
    const lightness = 0.55 + Math.sin(progress * Math.PI * 1.2) * 0.15  // 40%-70%

    // 将HSL转换为RGB
    return this.hslToRgb(hue % 360, saturation, lightness)
  }

  /**
   * HSL转RGB - 用于生成更鲜艳的随机颜色
   */
  private hslToRgb(h: number, s: number, l: number): IRGB {
    h = h / 360
    const c = (1 - Math.abs(2 * l - 1)) * s
    const x = c * (1 - Math.abs((h * 6) % 2 - 1))
    const m = l - c / 2

    let r = 0, g = 0, b = 0

    if (h >= 0 && h < 1/6) {
      r = c; g = x; b = 0
    } else if (h >= 1/6 && h < 2/6) {
      r = x; g = c; b = 0
    } else if (h >= 2/6 && h < 3/6) {
      r = 0; g = c; b = x
    } else if (h >= 3/6 && h < 4/6) {
      r = 0; g = x; b = c
    } else if (h >= 4/6 && h < 5/6) {
      r = x; g = 0; b = c
    } else if (h >= 5/6 && h < 1) {
      r = c; g = 0; b = x
    }

    return {
      r: Math.round((r + m) * 255),
      g: Math.round((g + m) * 255),
      b: Math.round((b + m) * 255)
    }
  }







  /**
   * 更新颜色进度（降低频率版本 - 更慢的颜色变化）
   */
  private updateColorProgress(distance: number): void {
    // 降低颜色变化频率，让颜色段落更长
    const speedMultiplier = 0.8 // 降低速度倍数
    const increment = (distance / 150) * this.colorChangeSpeed * speedMultiplier
    this.colorProgress += increment

    // 确保颜色进度在有效范围内
    if (this.colorProgress > 1) {
      this.colorProgress -= 1
    }
  }

  /**
   * 绘制渐变线段（简洁版本，基于test-gradient-brush.html）
   */
  private drawGradientSegment(p1: IGradientPoint, p2: IGradientPoint): void {
    const gradient = this.context.createLinearGradient(p1.x, p1.y, p2.x, p2.y)
    gradient.addColorStop(0, `rgb(${p1.color.r}, ${p1.color.g}, ${p1.color.b})`)
    gradient.addColorStop(1, `rgb(${p2.color.r}, ${p2.color.g}, ${p2.color.b})`)

    this.context.save()

    if (this.settingLockLayerAlpha) {
      this.context.globalCompositeOperation = 'source-atop'
    }

    this.context.strokeStyle = gradient
    this.context.lineWidth = (p1.size + p2.size) / 2
    this.context.lineCap = 'round'
    this.context.lineJoin = 'round'
    this.context.globalAlpha = (p1.opacity + p2.opacity) / 2

    this.context.beginPath()
    this.context.moveTo(p1.x, p1.y)
    this.context.lineTo(p2.x, p2.y)
    this.context.stroke()

    this.context.restore()
  }











  /**
   * 绘制渐变点
   */
  private drawGradientPoint(point: IGradientPoint): void {
    this.context.save()
    
    if (this.settingLockLayerAlpha) {
      this.context.globalCompositeOperation = 'source-atop'
    }

    this.context.globalAlpha = point.opacity
    this.context.fillStyle = `rgb(${point.color.r}, ${point.color.g}, ${point.color.b})`

    this.context.beginPath()
    this.context.arc(point.x, point.y, point.size / 2, 0, Math.PI * 2)
    this.context.fill()

    this.context.restore()
  }

  // ----------------------------------- public -----------------------------------

  startLine(x: number, y: number, pressure: number): void {
    this.historyEntry = {
      tool: ['brush', 'GradientBrush'],
      actions: [
        { action: 'setSize', params: [this.settingSize] },
        { action: 'setOpacity', params: [this.settingOpacity] },
        { action: 'setBaseColor', params: [this.settingBaseColor] },
        { action: 'setLockAlpha', params: [this.settingLockLayerAlpha] },
        { action: 'setSizePressure', params: [this.settingHasSizePressure] },
        { action: 'setOpacityPressure', params: [this.settingHasOpacityPressure] },
        { action: 'setGradientMode', params: [this.gradientMode] },
        { action: 'setColorChangeSpeed', params: [this.colorChangeSpeed] },
        { action: 'setColorRandomness', params: [this.colorRandomness] },
        { action: 'setGradientSmoothing', params: [this.gradientSmoothing] },
        { action: 'startLine', params: [x, y, pressure] }
      ]
    }

    // 计算初始颜色和大小
    const currentColor = this.calculateCurrentColor(this.colorProgress)
    const size = this.settingHasSizePressure ? 
      this.settingSize * (0.3 + pressure * 0.7) : this.settingSize
    const opacity = this.settingHasOpacityPressure ? 
      this.settingOpacity * pressure : this.settingOpacity

    const point: IGradientPoint = {
      x, y, pressure: BB.clamp(pressure, 0, 1),
      color: currentColor, size, opacity
    }

    this.strokePoints = [point]
    this.isDrawingState = true

    // 绘制起始点
    this.drawGradientPoint(point)

    this.inputArr = [{ x, y, pressure }]
    this.inputIsDrawing = true
  }

  goLine(x: number, y: number, pressure: number): void {
    if (!this.isDrawingState) return

    // 计算与上一个点的距离
    const lastPoint = this.strokePoints[this.strokePoints.length - 1]
    const distance = lastPoint ?
      Math.sqrt((x - lastPoint.x) ** 2 + (y - lastPoint.y) ** 2) : 0

    // 基于距离更新颜色进度
    this.updateColorProgress(distance)

    // 计算当前颜色和大小
    const currentColor = this.calculateCurrentColor(this.colorProgress)
    const size = this.settingHasSizePressure ?
      this.settingSize * (0.3 + pressure * 0.7) : this.settingSize
    const opacity = this.settingHasOpacityPressure ?
      this.settingOpacity * pressure : this.settingOpacity

    const currentPoint: IGradientPoint = {
      x, y, pressure: BB.clamp(pressure, 0, 1),
      color: currentColor, size, opacity
    }

    this.strokePoints.push(currentPoint)

    // 绘制渐变线段
    if (this.strokePoints.length >= 2) {
      const previousPoint = this.strokePoints[this.strokePoints.length - 2]
      this.drawGradientSegment(previousPoint, currentPoint)
    }

    this.historyEntry!.actions!.push({
      action: 'goLine',
      params: [x, y, pressure]
    })

    this.inputArr.push({ x, y, pressure })
  }

  endLine(x: number, y: number): void {
    if (!this.isDrawingState) return

    this.isDrawingState = false
    this.strokePoints = []

    this.historyEntry!.actions!.push({
      action: 'endLine',
      params: [x, y]
    })

    if (this.historyEntry) {
      this.history?.push(this.historyEntry)
      this.historyEntry = undefined
    }

    this.inputIsDrawing = false
  }

  // ---- interface ----

  isDrawing(): boolean {
    return this.inputIsDrawing
  }

  getIsDrawing(): boolean {
    return this.inputIsDrawing
  }

  setContext(c: CanvasRenderingContext2D): void {
    this.context = c
  }

  setHistory(h: KlHistory): void {
    this.history = h
  }

  setSize(size: number): void {
    this.settingSize = size
  }

  setOpacity(opacity: number): void {
    this.settingOpacity = opacity
  }

  setBaseColor(color: IRGB): void {
    this.settingBaseColor = { ...color }
  }

  // 为了兼容性，保留 setColor 方法
  setColor(color: IRGB): void {
    this.setBaseColor(color)
  }

  setLockAlpha(lock: boolean): void {
    this.settingLockLayerAlpha = lock
  }

  setSizePressure(b: boolean): void {
    this.settingHasSizePressure = b
  }

  setOpacityPressure(b: boolean): void {
    this.settingHasOpacityPressure = b
  }

  // 渐变参数设置方法
  setGradientMode(mode: 'rainbow' | 'warm' | 'cool' | 'custom'): void {
    this.gradientMode = mode
    console.log(`[GradientBrush] setGradientMode: ${this.gradientMode}`)
  }

  setColorChangeSpeed(value: number): void {
    this.colorChangeSpeed = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setColorChangeSpeed: ${this.colorChangeSpeed}`)
  }

  setColorRandomness(value: number): void {
    this.colorRandomness = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setColorRandomness: ${this.colorRandomness}`)
  }

  setGradientSmoothing(value: number): void {
    this.gradientSmoothing = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setGradientSmoothing: ${this.gradientSmoothing}`)
  }

  setColorTransitionSmoothing(value: number): void {
    this.colorTransitionSmoothing = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setColorTransitionSmoothing: ${this.colorTransitionSmoothing}`)
  }

  setCustomColors(colors: IRGB[]): void {
    if (colors.length > 0) {
      this.customColors = colors.map(c => ({ ...c }))
      console.log(`[GradientBrush] setCustomColors: ${colors.length} colors`)
    }
  }

  // Getters
  getSize(): number {
    return this.settingSize
  }

  getOpacity(): number {
    return this.settingOpacity
  }

  getBaseColor(): IRGB {
    return { ...this.settingBaseColor }
  }

  getLockAlpha(): boolean {
    return this.settingLockLayerAlpha
  }

  getSizePressure(): boolean {
    return this.settingHasSizePressure
  }

  getOpacityPressure(): boolean {
    return this.settingHasOpacityPressure
  }

  getGradientMode(): string {
    return this.gradientMode
  }

  getColorChangeSpeed(): number {
    return this.colorChangeSpeed
  }

  getColorRandomness(): number {
    return this.colorRandomness
  }

  getGradientSmoothing(): number {
    return this.gradientSmoothing
  }

  getColorTransitionSmoothing(): number {
    return this.colorTransitionSmoothing
  }

  setAntiColorBanding(value: number): void {
    this.antiColorBanding = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setAntiColorBanding: ${this.antiColorBanding}`)
  }

  getAntiColorBanding(): number {
    return this.antiColorBanding
  }

  getCustomColors(): IRGB[] {
    return this.customColors.map(c => ({ ...c }))
  }

  // 获取渐变参数（用于UI同步）
  getGradientParams(): {
    mode: string
    colorChangeSpeed: number
    colorRandomness: number
    gradientSmoothing: number
    customColors: IRGB[]
  } {
    return {
      mode: this.gradientMode,
      colorChangeSpeed: this.colorChangeSpeed,
      colorRandomness: this.colorRandomness,
      gradientSmoothing: this.gradientSmoothing,
      customColors: this.getCustomColors()
    }
  }
}
