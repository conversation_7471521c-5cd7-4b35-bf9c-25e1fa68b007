import { BB } from '../../bb/bb'
import { IRGB, TPressureInput } from '../kl-types'
import { IHistoryEntry, KlHistory, THistoryInnerActions } from '../history/kl-history'

export interface IGradientBrushHistoryEntry extends IHistoryEntry {
  tool: ['brush', 'GradientBrush']
  actions: THistoryInnerActions<GradientBrush>[]
}

interface IGradientPoint {
  x: number
  y: number
  pressure: number
  color: IRGB
  size: number
  opacity: number
}

/**
 * 渐变色画笔 - 实现边画边颜色随机变化的渐变效果
 * 支持多种渐变模式和颜色配置
 */
export class GradientBrush {
  private context: CanvasRenderingContext2D = {} as CanvasRenderingContext2D
  private history: KlHistory | undefined
  private historyEntry: IGradientBrushHistoryEntry | undefined

  // 基本设置
  private settingSize: number = 20
  private settingOpacity: number = 1
  private settingBaseColor: IRGB = { r: 255, g: 0, b: 0 } // 基础颜色
  private settingLockLayerAlpha: boolean = false
  private settingHasSizePressure: boolean = true
  private settingHasOpacityPressure: boolean = false

  // 渐变效果参数
  private gradientMode: 'rainbow' | 'warm' | 'cool' | 'custom' = 'rainbow'
  private colorChangeSpeed: number = 0.5 // 颜色变化速度 (0-1)
  private colorRandomness: number = 0.3 // 颜色随机性 (0-1)
  private gradientSmoothing: number = 0.7 // 渐变平滑度 (0-1)
  private colorTransitionSmoothing: number = 0.8 // 颜色过渡丝滑度 (0-1)
  private antiColorBanding: number = 0.8 // 抗色差强度 (0-1)

  // 颜色调色板（优化过渡更自然）
  private colorPalettes = {
    rainbow: [
      { r: 255, g: 0, b: 0 },     // 红
      { r: 255, g: 127, b: 0 },   // 橙红
      { r: 255, g: 200, b: 0 },   // 橙黄
      { r: 200, g: 255, b: 0 },   // 黄绿
      { r: 0, g: 255, b: 100 },   // 绿
      { r: 0, g: 200, b: 255 },   // 青绿
      { r: 0, g: 100, b: 255 },   // 蓝
      { r: 100, g: 0, b: 255 },   // 蓝紫
      { r: 200, g: 0, b: 200 },   // 紫
      { r: 255, g: 0, b: 100 }    // 紫红
    ],
    warm: [
      { r: 255, g: 60, b: 60 },   // 暖红
      { r: 255, g: 120, b: 40 },  // 橙红
      { r: 255, g: 180, b: 20 },  // 橙
      { r: 255, g: 220, b: 60 },  // 金橙
      { r: 255, g: 240, b: 120 }, // 暖黄
      { r: 255, g: 200, b: 80 },  // 金黄
      { r: 255, g: 140, b: 60 }   // 橙色
    ],
    cool: [
      { r: 100, g: 255, b: 255 }, // 浅青
      { r: 60, g: 220, b: 255 },  // 天蓝
      { r: 80, g: 160, b: 255 },  // 蓝
      { r: 120, g: 100, b: 255 }, // 蓝紫
      { r: 160, g: 80, b: 255 },  // 紫
      { r: 200, g: 120, b: 255 }, // 淡紫
      { r: 140, g: 180, b: 255 }  // 蓝色
    ],
    custom: [
      { r: 255, g: 107, b: 107 }, // 粉红
      { r: 255, g: 154, b: 77 },  // 橙粉
      { r: 255, g: 206, b: 84 },  // 金黄
      { r: 150, g: 206, b: 180 }, // 薄荷绿
      { r: 78, g: 205, b: 196 },  // 青绿
      { r: 69, g: 183, b: 209 },  // 天蓝
      { r: 162, g: 155, b: 254 }, // 淡紫
      { r: 221, g: 160, b: 221 }  // 紫色
    ]
  }

  // 绘制状态
  private strokePoints: IGradientPoint[] = []
  private colorProgress: number = 0
  private strokeColorProgress: number = 0 // 当前笔画的颜色进度（独立于全局进度）
  private inputArr: TPressureInput[] = []
  private inputIsDrawing: boolean = false
  private isDrawingState: boolean = false // 内部绘制状态

  constructor() {
    console.log(`[GradientBrush] Constructor called`)
  }

  /**
   * 重置绘制状态
   */
  resetState(): void {
    this.strokePoints = []
    this.colorProgress = 0
    this.strokeColorProgress = 0
    this.isDrawingState = false
  }



  /**
   * 平滑步进函数
   */
  private smoothStep(t: number): number {
    return t * t * (3 - 2 * t)
  }

  /**
   * 计算当前应该使用的颜色（使用平滑插值）
   */
  private calculateCurrentColor(progress: number): IRGB {
    const palette = this.colorPalettes[this.gradientMode]

    // 使用平滑的随机性
    const smoothRandom = Math.sin(Date.now() * 0.001) * this.colorRandomness * 0.1
    const adjustedProgress = Math.max(0, Math.min(1, progress + smoothRandom))

    // 使用平滑曲线
    const smoothProgress = this.smoothStep(adjustedProgress)

    const position = smoothProgress * (palette.length - 1)
    const index1 = Math.floor(position)
    const index2 = Math.min(index1 + 1, palette.length - 1)
    const t = position - index1

    const color1 = palette[index1]
    const color2 = palette[index2]

    // 使用平滑插值
    const smoothT = this.smoothStep(t)

    return {
      r: Math.round(color1.r + (color2.r - color1.r) * smoothT),
      g: Math.round(color1.g + (color2.g - color1.g) * smoothT),
      b: Math.round(color1.b + (color2.b - color1.b) * smoothT)
    }
  }







  /**
   * 更新全局颜色进度（基于test-gradient-brush.html的简化版本）
   */
  private updateColorProgress(): void {
    // 更新颜色进度
    this.colorProgress += this.colorChangeSpeed * 0.01
    if (this.colorProgress > 1) {
      this.colorProgress = 0
    }
  }

  /**
   * 更新当前笔画的颜色进度（独立于全局进度）
   */
  private updateStrokeColorProgress(): void {
    // 更新当前笔画的颜色进度
    this.strokeColorProgress += this.colorChangeSpeed * 0.01
    if (this.strokeColorProgress > 1) {
      this.strokeColorProgress = 0
    }
  }

  /**
   * 绘制渐变线段（简洁版本，基于test-gradient-brush.html）
   */
  private drawGradientSegment(p1: IGradientPoint, p2: IGradientPoint): void {
    const gradient = this.context.createLinearGradient(p1.x, p1.y, p2.x, p2.y)
    gradient.addColorStop(0, `rgb(${p1.color.r}, ${p1.color.g}, ${p1.color.b})`)
    gradient.addColorStop(1, `rgb(${p2.color.r}, ${p2.color.g}, ${p2.color.b})`)

    this.context.save()

    if (this.settingLockLayerAlpha) {
      this.context.globalCompositeOperation = 'source-atop'
    }

    this.context.strokeStyle = gradient
    this.context.lineWidth = (p1.size + p2.size) / 2
    this.context.lineCap = 'round'
    this.context.lineJoin = 'round'
    this.context.globalAlpha = (p1.opacity + p2.opacity) / 2

    this.context.beginPath()
    this.context.moveTo(p1.x, p1.y)
    this.context.lineTo(p2.x, p2.y)
    this.context.stroke()

    this.context.restore()
  }











  /**
   * 绘制渐变点
   */
  private drawGradientPoint(point: IGradientPoint): void {
    this.context.save()
    
    if (this.settingLockLayerAlpha) {
      this.context.globalCompositeOperation = 'source-atop'
    }

    this.context.globalAlpha = point.opacity
    this.context.fillStyle = `rgb(${point.color.r}, ${point.color.g}, ${point.color.b})`

    this.context.beginPath()
    this.context.arc(point.x, point.y, point.size / 2, 0, Math.PI * 2)
    this.context.fill()

    this.context.restore()
  }

  // ----------------------------------- public -----------------------------------

  startLine(x: number, y: number, pressure: number): void {
    // 为当前笔画设置独立的颜色进度起点
    this.strokeColorProgress = this.colorProgress

    this.historyEntry = {
      tool: ['brush', 'GradientBrush'],
      actions: [
        { action: 'setSize', params: [this.settingSize] },
        { action: 'setOpacity', params: [this.settingOpacity] },
        { action: 'setBaseColor', params: [this.settingBaseColor] },
        { action: 'setLockAlpha', params: [this.settingLockLayerAlpha] },
        { action: 'setSizePressure', params: [this.settingHasSizePressure] },
        { action: 'setOpacityPressure', params: [this.settingHasOpacityPressure] },
        { action: 'setGradientMode', params: [this.gradientMode] },
        { action: 'setColorChangeSpeed', params: [this.colorChangeSpeed] },
        { action: 'setColorRandomness', params: [this.colorRandomness] },
        { action: 'setGradientSmoothing', params: [this.gradientSmoothing] },
        { action: 'setStrokeColorProgress', params: [this.strokeColorProgress] }, // 保存笔画起始颜色进度
      ]
    }

    // 计算初始颜色和大小
    const currentColor = this.calculateCurrentColor(this.strokeColorProgress)
    const size = this.settingHasSizePressure ?
      this.settingSize * (0.3 + pressure * 0.7) : this.settingSize
    const opacity = this.settingHasOpacityPressure ?
      this.settingOpacity * pressure : this.settingOpacity

    const point: IGradientPoint = {
      x, y, pressure: BB.clamp(pressure, 0, 1),
      color: currentColor, size, opacity
    }

    this.strokePoints = [point]
    this.isDrawingState = true

    // 绘制起始点
    this.drawGradientPoint(point)

    // 将起始点的具体颜色值保存到历史记录中（关键修复）
    this.historyEntry.actions.push({
      action: 'startLineWithColor',
      params: [x, y, pressure, currentColor] // 直接保存颜色值
    })

    this.inputArr = [{ x, y, pressure }]
    this.inputIsDrawing = true
  }

  goLine(x: number, y: number, pressure: number): void {
    if (!this.isDrawingState) return

    // 更新当前笔画的颜色进度（独立于全局进度）
    this.updateStrokeColorProgress()

    // 计算当前颜色和大小（使用笔画独立的颜色进度）
    const currentColor = this.calculateCurrentColor(this.strokeColorProgress)
    const size = this.settingHasSizePressure ?
      this.settingSize * (0.3 + pressure * 0.7) : this.settingSize
    const opacity = this.settingHasOpacityPressure ?
      this.settingOpacity * pressure : this.settingOpacity

    const currentPoint: IGradientPoint = {
      x, y, pressure: BB.clamp(pressure, 0, 1),
      color: currentColor, size, opacity
    }

    this.strokePoints.push(currentPoint)

    // 绘制渐变线段
    if (this.strokePoints.length >= 2) {
      const previousPoint = this.strokePoints[this.strokePoints.length - 2]
      this.drawGradientSegment(previousPoint, currentPoint)
    }

    // 保存具体的颜色值到历史记录中（关键修复）
    this.historyEntry!.actions!.push({
      action: 'goLineWithColor',
      params: [x, y, pressure, currentColor] // 直接保存颜色值
    })

    this.inputArr.push({ x, y, pressure })
  }

  endLine(x: number, y: number): void {
    if (!this.isDrawingState) return

    this.isDrawingState = false
    this.strokePoints = []

    // 笔画结束时，将当前笔画的颜色进度同步到全局进度
    // 这样下一个笔画会从当前位置继续
    this.colorProgress = this.strokeColorProgress

    this.historyEntry!.actions!.push({
      action: 'endLine',
      params: [x, y]
    })

    if (this.historyEntry) {
      this.history?.push(this.historyEntry)
      this.historyEntry = undefined
    }

    this.inputIsDrawing = false
  }

  // ---- interface ----

  isDrawing(): boolean {
    return this.inputIsDrawing
  }

  getIsDrawing(): boolean {
    return this.inputIsDrawing
  }

  setContext(c: CanvasRenderingContext2D): void {
    this.context = c
  }

  setHistory(h: KlHistory): void {
    this.history = h
  }

  setSize(size: number): void {
    this.settingSize = size
  }

  setOpacity(opacity: number): void {
    this.settingOpacity = opacity
  }

  setBaseColor(color: IRGB): void {
    this.settingBaseColor = { ...color }
  }

  // 为了兼容性，保留 setColor 方法
  setColor(color: IRGB): void {
    this.setBaseColor(color)
  }

  setLockAlpha(lock: boolean): void {
    this.settingLockLayerAlpha = lock
  }

  setSizePressure(b: boolean): void {
    this.settingHasSizePressure = b
  }

  setOpacityPressure(b: boolean): void {
    this.settingHasOpacityPressure = b
  }

  // 渐变参数设置方法
  setGradientMode(mode: 'rainbow' | 'warm' | 'cool' | 'custom'): void {
    this.gradientMode = mode
    console.log(`[GradientBrush] setGradientMode: ${this.gradientMode}`)
  }

  setColorChangeSpeed(value: number): void {
    this.colorChangeSpeed = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setColorChangeSpeed: ${this.colorChangeSpeed}`)
  }

  setColorRandomness(value: number): void {
    this.colorRandomness = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setColorRandomness: ${this.colorRandomness}`)
  }

  setGradientSmoothing(value: number): void {
    this.gradientSmoothing = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setGradientSmoothing: ${this.gradientSmoothing}`)
  }

  setColorTransitionSmoothing(value: number): void {
    this.colorTransitionSmoothing = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setColorTransitionSmoothing: ${this.colorTransitionSmoothing}`)
  }

  setStrokeColorProgress(value: number): void {
    this.strokeColorProgress = value
    console.log(`[GradientBrush] setStrokeColorProgress: ${this.strokeColorProgress}`)
  }

  /**
   * 使用指定颜色开始绘制线条（用于历史重放）
   */
  startLineWithColor(x: number, y: number, pressure: number, color: IRGB): void {
    const size = this.settingHasSizePressure ?
      this.settingSize * (0.3 + pressure * 0.7) : this.settingSize
    const opacity = this.settingHasOpacityPressure ?
      this.settingOpacity * pressure : this.settingOpacity

    const point: IGradientPoint = {
      x, y, pressure: BB.clamp(pressure, 0, 1),
      color: color, // 使用传入的固定颜色
      size, opacity
    }

    this.strokePoints = [point]
    this.isDrawingState = true

    // 绘制起始点
    this.drawGradientPoint(point)

    this.inputArr = [{ x, y, pressure }]
    this.inputIsDrawing = true
  }

  /**
   * 使用指定颜色继续绘制线条（用于历史重放）
   */
  goLineWithColor(x: number, y: number, pressure: number, color: IRGB): void {
    if (!this.isDrawingState) return

    const size = this.settingHasSizePressure ?
      this.settingSize * (0.3 + pressure * 0.7) : this.settingSize
    const opacity = this.settingHasOpacityPressure ?
      this.settingOpacity * pressure : this.settingOpacity

    const currentPoint: IGradientPoint = {
      x, y, pressure: BB.clamp(pressure, 0, 1),
      color: color, // 使用传入的固定颜色
      size, opacity
    }

    this.strokePoints.push(currentPoint)

    // 绘制渐变线段
    if (this.strokePoints.length >= 2) {
      const previousPoint = this.strokePoints[this.strokePoints.length - 2]
      this.drawGradientSegment(previousPoint, currentPoint)
    }

    this.inputArr.push({ x, y, pressure })
  }

  setCustomColors(colors: IRGB[]): void {
    if (colors.length > 0) {
      this.colorPalettes.custom = colors.map(c => ({ ...c }))
      console.log(`[GradientBrush] setCustomColors: ${colors.length} colors`)
    }
  }

  // Getters
  getSize(): number {
    return this.settingSize
  }

  getOpacity(): number {
    return this.settingOpacity
  }

  getBaseColor(): IRGB {
    return { ...this.settingBaseColor }
  }

  getLockAlpha(): boolean {
    return this.settingLockLayerAlpha
  }

  getSizePressure(): boolean {
    return this.settingHasSizePressure
  }

  getOpacityPressure(): boolean {
    return this.settingHasOpacityPressure
  }

  getGradientMode(): string {
    return this.gradientMode
  }

  getColorChangeSpeed(): number {
    return this.colorChangeSpeed
  }

  getColorRandomness(): number {
    return this.colorRandomness
  }

  getGradientSmoothing(): number {
    return this.gradientSmoothing
  }

  getColorTransitionSmoothing(): number {
    return this.colorTransitionSmoothing
  }

  setAntiColorBanding(value: number): void {
    this.antiColorBanding = BB.clamp(value, 0, 1)
    console.log(`[GradientBrush] setAntiColorBanding: ${this.antiColorBanding}`)
  }

  getAntiColorBanding(): number {
    return this.antiColorBanding
  }

  getCustomColors(): IRGB[] {
    return this.colorPalettes.custom.map(c => ({ ...c }))
  }

  // 获取渐变参数（用于UI同步）
  getGradientParams(): {
    mode: string
    colorChangeSpeed: number
    colorRandomness: number
    gradientSmoothing: number
    customColors: IRGB[]
  } {
    return {
      mode: this.gradientMode,
      colorChangeSpeed: this.colorChangeSpeed,
      colorRandomness: this.colorRandomness,
      gradientSmoothing: this.gradientSmoothing,
      customColors: this.getCustomColors()
    }
  }
}
