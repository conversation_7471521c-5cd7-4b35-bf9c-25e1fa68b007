<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渐变画笔撤销修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 4px;
            cursor: crosshair;
            background: white;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            color: #155724;
        }
        .info {
            margin-top: 15px;
            padding: 10px;
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .test-steps {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 渐变画笔撤销修复测试</h1>
        
        <div class="test-steps">
            <h3>测试步骤：</h3>
            <ol>
                <li>点击"绘制测试线条1"按钮，绘制第一条渐变线</li>
                <li>点击"绘制测试线条2"按钮，绘制第二条渐变线（颜色应该从第一条结束的地方继续）</li>
                <li>点击"绘制测试线条3"按钮，绘制第三条渐变线</li>
                <li>点击"撤销"按钮，观察已绘制的线条颜色是否保持不变</li>
                <li>多次撤销和重做，验证颜色稳定性</li>
            </ol>
            <p><strong>预期结果：</strong>撤销时，已完成的线条颜色应该保持不变，不会因为撤销操作而改变颜色。</p>
        </div>
        
        <div class="controls">
            <button class="btn-primary" onclick="drawTestLine1()">绘制测试线条1</button>
            <button class="btn-primary" onclick="drawTestLine2()">绘制测试线条2</button>
            <button class="btn-primary" onclick="drawTestLine3()">绘制测试线条3</button>
            <button class="btn-danger" onclick="simulateUndo()">撤销</button>
            <button class="btn-success" onclick="simulateRedo()">重做</button>
            <button class="btn-secondary" onclick="clearCanvas()">清空画布</button>
            <button class="btn-secondary" onclick="resetProgress()">重置颜色进度</button>
        </div>

        <canvas id="testCanvas" width="800" height="400"></canvas>
        
        <div class="status" id="status">
            <strong>状态:</strong> 准备测试 - 点击按钮开始测试撤销修复功能
        </div>
        
        <div class="info">
            <h4>修复说明：</h4>
            <ul>
                <li><strong>问题：</strong>撤销时已完成的渐变线条颜色会发生变化</li>
                <li><strong>原因：</strong>全局colorProgress状态在撤销重放时被重置，影响已绘制线条</li>
                <li><strong>解决方案：</strong>为每个笔画保存独立的颜色进度状态</li>
                <li><strong>实现：</strong>使用strokeColorProgress记录每个笔画的颜色进度</li>
                <li><strong>历史记录：</strong>在历史记录中保存setStrokeColorProgress动作</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟修复后的渐变画笔测试
        const canvas = document.getElementById('testCanvas');
        const ctx = canvas.getContext('2d');
        const statusEl = document.getElementById('status');
        
        // 模拟画笔状态
        let globalColorProgress = 0;
        let strokeHistory = []; // 保存每个笔画的历史记录
        let currentHistoryIndex = -1;
        
        // 颜色调色板
        const colorPalette = [
            { r: 255, g: 0, b: 0 },     // 红
            { r: 255, g: 127, b: 0 },   // 橙红
            { r: 255, g: 200, b: 0 },   // 橙黄
            { r: 200, g: 255, b: 0 },   // 黄绿
            { r: 0, g: 255, b: 100 },   // 绿
            { r: 0, g: 200, b: 255 },   // 青绿
            { r: 0, g: 100, b: 255 },   // 蓝
            { r: 100, g: 0, b: 255 },   // 蓝紫
            { r: 200, g: 0, b: 200 },   // 紫
            { r: 255, g: 0, b: 100 }    // 紫红
        ];
        
        // 平滑步进函数
        function smoothStep(t) {
            return t * t * (3 - 2 * t);
        }

        // 计算颜色
        function calculateColor(progress) {
            const smoothRandom = Math.sin(Date.now() * 0.001) * 0.3 * 0.1;
            const adjustedProgress = Math.max(0, Math.min(1, progress + smoothRandom));
            const smoothProgress = smoothStep(adjustedProgress);

            const position = smoothProgress * (colorPalette.length - 1);
            const index1 = Math.floor(position);
            const index2 = Math.min(index1 + 1, colorPalette.length - 1);
            const t = position - index1;

            const color1 = colorPalette[index1];
            const color2 = colorPalette[index2];
            const smoothT = smoothStep(t);

            return {
                r: Math.round(color1.r + (color2.r - color1.r) * smoothT),
                g: Math.round(color1.g + (color2.g - color1.g) * smoothT),
                b: Math.round(color1.b + (color2.b - color1.b) * smoothT)
            };
        }
        
        // 绘制渐变线段
        function drawGradientSegment(p1, p2) {
            const gradient = ctx.createLinearGradient(p1.x, p1.y, p2.x, p2.y);
            gradient.addColorStop(0, `rgb(${p1.color.r}, ${p1.color.g}, ${p1.color.b})`);
            gradient.addColorStop(1, `rgb(${p2.color.r}, ${p2.color.g}, ${p2.color.b})`);
            
            ctx.strokeStyle = gradient;
            ctx.lineWidth = 15;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            ctx.beginPath();
            ctx.moveTo(p1.x, p1.y);
            ctx.lineTo(p2.x, p2.y);
            ctx.stroke();
        }
        
        // 绘制测试线条（模拟修复后的行为）
        function drawTestLine(startX, startY, endX, endY, lineNumber) {
            // 为当前笔画设置独立的颜色进度起点（修复的关键）
            const strokeColorProgress = globalColorProgress;
            
            // 创建笔画历史记录
            const strokeRecord = {
                startX, startY, endX, endY,
                strokeColorProgress: strokeColorProgress, // 保存笔画起始颜色进度
                points: []
            };
            
            const steps = 50;
            let currentStrokeProgress = strokeColorProgress;
            
            for (let i = 0; i <= steps; i++) {
                const x = startX + (endX - startX) * (i / steps);
                const y = startY + (endY - startY) * (i / steps);
                
                // 更新当前笔画的颜色进度（独立于全局进度）
                if (i > 0) {
                    currentStrokeProgress += 0.5 * 0.01; // 模拟colorChangeSpeed * 0.01
                    if (currentStrokeProgress > 1) {
                        currentStrokeProgress = 0;
                    }
                }
                
                const color = calculateColor(currentStrokeProgress);
                const point = { x, y, color };
                strokeRecord.points.push(point);
                
                if (i > 0) {
                    const prevPoint = strokeRecord.points[i - 1];
                    drawGradientSegment(prevPoint, point);
                }
            }
            
            // 笔画结束时，将当前笔画的颜色进度同步到全局进度
            globalColorProgress = currentStrokeProgress;
            
            // 保存到历史记录
            strokeHistory = strokeHistory.slice(0, currentHistoryIndex + 1);
            strokeHistory.push(strokeRecord);
            currentHistoryIndex = strokeHistory.length - 1;
            
            statusEl.innerHTML = `<strong>状态:</strong> 绘制完成线条${lineNumber}，全局颜色进度: ${globalColorProgress.toFixed(3)}`;
        }
        
        function drawTestLine1() {
            drawTestLine(50, 100, 350, 150, 1);
        }
        
        function drawTestLine2() {
            drawTestLine(100, 200, 400, 250, 2);
        }
        
        function drawTestLine3() {
            drawTestLine(150, 300, 450, 350, 3);
        }
        
        // 模拟撤销（重新绘制所有历史记录）
        function simulateUndo() {
            if (currentHistoryIndex < 0) {
                statusEl.innerHTML = '<strong>状态:</strong> 无法撤销，没有更多历史记录';
                return;
            }
            
            currentHistoryIndex--;
            redrawFromHistory();
            statusEl.innerHTML = `<strong>状态:</strong> 撤销完成，当前历史索引: ${currentHistoryIndex}`;
        }
        
        // 模拟重做
        function simulateRedo() {
            if (currentHistoryIndex >= strokeHistory.length - 1) {
                statusEl.innerHTML = '<strong>状态:</strong> 无法重做，已经是最新状态';
                return;
            }
            
            currentHistoryIndex++;
            redrawFromHistory();
            statusEl.innerHTML = `<strong>状态:</strong> 重做完成，当前历史索引: ${currentHistoryIndex}`;
        }
        
        // 从历史记录重新绘制（模拟修复后的行为）
        function redrawFromHistory() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 重新绘制所有历史记录中的笔画
            for (let i = 0; i <= currentHistoryIndex; i++) {
                const stroke = strokeHistory[i];
                
                // 使用保存的笔画颜色进度重新绘制（关键修复点）
                for (let j = 1; j < stroke.points.length; j++) {
                    const prevPoint = stroke.points[j - 1];
                    const currentPoint = stroke.points[j];
                    drawGradientSegment(prevPoint, currentPoint);
                }
            }
            
            // 更新全局颜色进度到最后一个笔画的结束位置
            if (currentHistoryIndex >= 0) {
                const lastStroke = strokeHistory[currentHistoryIndex];
                // 这里应该计算最后一个笔画的结束颜色进度
                // 为简化演示，我们使用一个近似值
                globalColorProgress = lastStroke.strokeColorProgress + (lastStroke.points.length - 1) * 0.5 * 0.01;
                if (globalColorProgress > 1) globalColorProgress -= Math.floor(globalColorProgress);
            } else {
                globalColorProgress = 0;
            }
        }
        
        function clearCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            strokeHistory = [];
            currentHistoryIndex = -1;
            globalColorProgress = 0;
            statusEl.innerHTML = '<strong>状态:</strong> 画布已清空，历史记录已重置';
        }
        
        function resetProgress() {
            globalColorProgress = 0;
            statusEl.innerHTML = '<strong>状态:</strong> 颜色进度已重置为0';
        }
        
        // 初始化
        statusEl.innerHTML = '<strong>状态:</strong> 测试环境已准备就绪 - 开始测试撤销修复功能';
    </script>
</body>
</html>
