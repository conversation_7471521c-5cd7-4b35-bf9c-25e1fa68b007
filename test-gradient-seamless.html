<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渐变画笔无缝过渡测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        
        .canvas-container {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .canvas-wrapper {
            flex: 1;
            text-align: center;
        }
        
        .canvas-wrapper h3 {
            margin: 0 0 10px 0;
            color: #666;
        }
        
        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: crosshair;
            background: white;
        }
        
        .controls {
            display: flex;
            gap: 20px;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }
        
        .control-group label {
            font-size: 12px;
            color: #666;
            font-weight: bold;
        }
        
        input[type="range"] {
            width: 120px;
        }
        
        .value-display {
            font-size: 11px;
            color: #888;
            min-width: 40px;
            text-align: center;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 12px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
        
        .info h4 {
            margin: 0 0 10px 0;
            color: #0056b3;
        }
        
        .info ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .info li {
            margin-bottom: 5px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎨 渐变画笔无缝过渡测试</h1>
        
        <div class="info">
            <h4>测试说明</h4>
            <ul>
                <li><strong>左侧画布</strong>：优化前的渐变效果（可能有色差痕迹）</li>
                <li><strong>右侧画布</strong>：优化后的无缝渐变效果</li>
                <li>在画布上拖拽绘制，观察颜色过渡的平滑程度</li>
                <li>调整下方参数，实时查看效果变化</li>
                <li>重点观察颜色过渡处是否有明显的色差痕迹</li>
            </ul>
        </div>
        
        <div class="canvas-container">
            <div class="canvas-wrapper">
                <h3>优化前（标准渐变）</h3>
                <canvas id="canvas1" width="400" height="300"></canvas>
            </div>
            <div class="canvas-wrapper">
                <h3>优化后（无缝渐变）</h3>
                <canvas id="canvas2" width="400" height="300"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>颜色过渡丝滑度</label>
                <input type="range" id="smoothness" min="0" max="100" value="80">
                <div class="value-display" id="smoothnessValue">80%</div>
            </div>
            <div class="control-group">
                <label>抗色差强度</label>
                <input type="range" id="antiBanding" min="0" max="100" value="80">
                <div class="value-display" id="antiBandingValue">80%</div>
            </div>
            <div class="control-group">
                <label>画笔大小</label>
                <input type="range" id="brushSize" min="5" max="50" value="20">
                <div class="value-display" id="brushSizeValue">20px</div>
            </div>
            <div class="control-group">
                <label>颜色变化速度</label>
                <input type="range" id="colorSpeed" min="0" max="100" value="50">
                <div class="value-display" id="colorSpeedValue">50%</div>
            </div>
        </div>
        
        <div class="test-buttons">
            <button onclick="clearCanvases()">清空画布</button>
            <button onclick="drawTestPattern()">绘制测试图案</button>
            <button onclick="drawRainbowLine()">绘制彩虹线条</button>
            <button onclick="drawGradientCircle()">绘制渐变圆圈</button>
        </div>
    </div>

    <script>
        // 画布和上下文
        const canvas1 = document.getElementById('canvas1');
        const canvas2 = document.getElementById('canvas2');
        const ctx1 = canvas1.getContext('2d');
        const ctx2 = canvas2.getContext('2d');
        
        // 渐变参数
        let gradientParams = {
            smoothness: 0.8,
            antiBanding: 0.8,
            brushSize: 20,
            colorSpeed: 0.5,
            colorProgress: 0
        };
        
        // 彩虹色调色板
        const rainbowColors = [
            {r: 255, g: 0, b: 0},    // 红
            {r: 255, g: 165, b: 0},  // 橙
            {r: 255, g: 255, b: 0},  // 黄
            {r: 0, g: 255, b: 0},    // 绿
            {r: 0, g: 0, b: 255},    // 蓝
            {r: 75, g: 0, b: 130},   // 靛
            {r: 148, g: 0, b: 211}   // 紫
        ];
        
        // 绘制状态
        let isDrawing = false;
        let lastPoint = null;
        
        // 控件更新
        function updateControls() {
            document.getElementById('smoothnessValue').textContent = Math.round(gradientParams.smoothness * 100) + '%';
            document.getElementById('antiBandingValue').textContent = Math.round(gradientParams.antiBanding * 100) + '%';
            document.getElementById('brushSizeValue').textContent = gradientParams.brushSize + 'px';
            document.getElementById('colorSpeedValue').textContent = Math.round(gradientParams.colorSpeed * 100) + '%';
        }
        
        // 事件监听器
        document.getElementById('smoothness').addEventListener('input', (e) => {
            gradientParams.smoothness = e.target.value / 100;
            updateControls();
        });
        
        document.getElementById('antiBanding').addEventListener('input', (e) => {
            gradientParams.antiBanding = e.target.value / 100;
            updateControls();
        });
        
        document.getElementById('brushSize').addEventListener('input', (e) => {
            gradientParams.brushSize = parseInt(e.target.value);
            updateControls();
        });
        
        document.getElementById('colorSpeed').addEventListener('input', (e) => {
            gradientParams.colorSpeed = e.target.value / 100;
            updateControls();
        });
        
        // 初始化控件显示
        updateControls();
        
        // 平滑步进函数
        function smoothStep(t) {
            return t * t * (3 - 2 * t);
        }
        
        // 增强平滑步进函数
        function enhancedSmoothStep(t) {
            if (t <= 0) return 0;
            if (t >= 1) return 1;
            const t3 = t * t * t;
            const t4 = t3 * t;
            const t5 = t4 * t;
            return 6 * t5 - 15 * t4 + 10 * t3;
        }
        
        // 颜色插值
        function interpolateColor(color1, color2, t, useSmoothing = false) {
            const smoothT = useSmoothing ? enhancedSmoothStep(t) : t;
            return {
                r: Math.round(color1.r + (color2.r - color1.r) * smoothT),
                g: Math.round(color1.g + (color2.g - color1.g) * smoothT),
                b: Math.round(color1.b + (color2.b - color1.b) * smoothT)
            };
        }
        
        // 获取当前颜色
        function getCurrentColor(progress) {
            const paletteLength = rainbowColors.length;
            const position = progress * (paletteLength - 1);
            const index1 = Math.floor(position);
            const index2 = Math.min(index1 + 1, paletteLength - 1);
            const t = position - index1;
            
            return interpolateColor(rainbowColors[index1], rainbowColors[index2], t, true);
        }
        
        // 标准渐变绘制（优化前）
        function drawStandardGradient(ctx, x1, y1, x2, y2, color1, color2) {
            const gradient = ctx.createLinearGradient(x1, y1, x2, y2);
            gradient.addColorStop(0, `rgb(${color1.r}, ${color1.g}, ${color1.b})`);
            gradient.addColorStop(1, `rgb(${color2.r}, ${color2.g}, ${color2.b})`);
            
            ctx.strokeStyle = gradient;
            ctx.lineWidth = gradientParams.brushSize;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            ctx.beginPath();
            ctx.moveTo(x1, y1);
            ctx.lineTo(x2, y2);
            ctx.stroke();
        }
        
        // 无缝渐变绘制（优化后）
        function drawSeamlessGradient(ctx, x1, y1, x2, y2, color1, color2) {
            const distance = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
            if (distance < 0.1) return;
            
            // 基于丝滑度调整分段数
            const segments = Math.max(5, Math.floor(distance / 2 * (1 + gradientParams.smoothness * 2)));
            const overlapFactor = 0.15; // 重叠因子
            
            for (let i = 0; i < segments; i++) {
                const t1 = Math.max(0, (i - overlapFactor) / segments);
                const t2 = Math.min(1, (i + 1 + overlapFactor) / segments);
                
                const smoothT1 = enhancedSmoothStep(t1);
                const smoothT2 = enhancedSmoothStep(t2);
                
                const sx1 = x1 + (x2 - x1) * smoothT1;
                const sy1 = y1 + (y2 - y1) * smoothT1;
                const sx2 = x1 + (x2 - x1) * smoothT2;
                const sy2 = y1 + (y2 - y1) * smoothT2;
                
                const segColor1 = interpolateColor(color1, color2, t1, true);
                const segColor2 = interpolateColor(color1, color2, t2, true);
                
                // 多层绘制消除接缝
                const layers = gradientParams.antiBanding > 0.8 ? 3 : 2;
                
                for (let layer = 0; layer < layers; layer++) {
                    ctx.save();
                    
                    if (layer === 0) {
                        ctx.globalCompositeOperation = 'source-over';
                        ctx.globalAlpha = 0.8 + gradientParams.antiBanding * 0.2;
                    } else if (layer === 1) {
                        ctx.globalCompositeOperation = 'multiply';
                        ctx.globalAlpha = 0.3;
                    } else {
                        ctx.globalCompositeOperation = 'overlay';
                        ctx.globalAlpha = 0.15;
                    }
                    
                    const gradient = ctx.createLinearGradient(sx1, sy1, sx2, sy2);
                    gradient.addColorStop(0, `rgb(${segColor1.r}, ${segColor1.g}, ${segColor1.b})`);
                    gradient.addColorStop(1, `rgb(${segColor2.r}, ${segColor2.g}, ${segColor2.b})`);
                    
                    ctx.strokeStyle = gradient;
                    ctx.lineWidth = gradientParams.brushSize * (1 - layer * 0.1);
                    ctx.lineCap = 'round';
                    ctx.lineJoin = 'round';
                    
                    ctx.beginPath();
                    ctx.moveTo(sx1, sy1);
                    ctx.lineTo(sx2, sy2);
                    ctx.stroke();
                    
                    ctx.restore();
                }
            }
        }
        
        // 清空画布
        function clearCanvases() {
            ctx1.clearRect(0, 0, canvas1.width, canvas1.height);
            ctx2.clearRect(0, 0, canvas2.width, canvas2.height);
            gradientParams.colorProgress = 0;
        }
        
        // 绘制测试图案
        function drawTestPattern() {
            clearCanvases();
            
            // 绘制水平渐变线条
            for (let i = 0; i < 5; i++) {
                const y = 50 + i * 50;
                const color1 = getCurrentColor(i * 0.2);
                const color2 = getCurrentColor((i + 1) * 0.2);
                
                drawStandardGradient(ctx1, 50, y, 350, y, color1, color2);
                drawSeamlessGradient(ctx2, 50, y, 350, y, color1, color2);
            }
        }
        
        // 绘制彩虹线条
        function drawRainbowLine() {
            clearCanvases();
            
            const segments = 20;
            for (let i = 0; i < segments; i++) {
                const x1 = 50 + (300 / segments) * i;
                const x2 = 50 + (300 / segments) * (i + 1);
                const y = 150;
                
                const color1 = getCurrentColor(i / segments);
                const color2 = getCurrentColor((i + 1) / segments);
                
                drawStandardGradient(ctx1, x1, y, x2, y, color1, color2);
                drawSeamlessGradient(ctx2, x1, y, x2, y, color1, color2);
            }
        }
        
        // 绘制渐变圆圈
        function drawGradientCircle() {
            clearCanvases();
            
            const centerX = 200;
            const centerY = 150;
            const radius = 80;
            const segments = 36;
            
            for (let i = 0; i < segments; i++) {
                const angle1 = (i / segments) * Math.PI * 2;
                const angle2 = ((i + 1) / segments) * Math.PI * 2;
                
                const x1 = centerX + Math.cos(angle1) * radius;
                const y1 = centerY + Math.sin(angle1) * radius;
                const x2 = centerX + Math.cos(angle2) * radius;
                const y2 = centerY + Math.sin(angle2) * radius;
                
                const color1 = getCurrentColor(i / segments);
                const color2 = getCurrentColor((i + 1) / segments);
                
                drawStandardGradient(ctx1, x1, y1, x2, y2, color1, color2);
                drawSeamlessGradient(ctx2, x1, y1, x2, y2, color1, color2);
            }
        }
        
        // 鼠标绘制事件
        function setupDrawing(canvas, ctx, isSeamless) {
            let drawing = false;
            let lastPos = null;
            
            canvas.addEventListener('mousedown', (e) => {
                drawing = true;
                const rect = canvas.getBoundingClientRect();
                lastPos = {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };
            });
            
            canvas.addEventListener('mousemove', (e) => {
                if (!drawing || !lastPos) return;
                
                const rect = canvas.getBoundingClientRect();
                const currentPos = {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };
                
                const distance = Math.sqrt(
                    (currentPos.x - lastPos.x) ** 2 + 
                    (currentPos.y - lastPos.y) ** 2
                );
                
                gradientParams.colorProgress += (distance / 100) * gradientParams.colorSpeed;
                if (gradientParams.colorProgress > 1) gradientParams.colorProgress -= 1;
                
                const color1 = getCurrentColor(gradientParams.colorProgress);
                const color2 = getCurrentColor((gradientParams.colorProgress + 0.1) % 1);
                
                if (isSeamless) {
                    drawSeamlessGradient(ctx, lastPos.x, lastPos.y, currentPos.x, currentPos.y, color1, color2);
                } else {
                    drawStandardGradient(ctx, lastPos.x, lastPos.y, currentPos.x, currentPos.y, color1, color2);
                }
                
                lastPos = currentPos;
            });
            
            canvas.addEventListener('mouseup', () => {
                drawing = false;
                lastPos = null;
            });
            
            canvas.addEventListener('mouseleave', () => {
                drawing = false;
                lastPos = null;
            });
        }
        
        // 设置绘制事件
        setupDrawing(canvas1, ctx1, false);
        setupDrawing(canvas2, ctx2, true);
        
        // 初始绘制测试图案
        drawTestPattern();
    </script>
</body>
</html>
