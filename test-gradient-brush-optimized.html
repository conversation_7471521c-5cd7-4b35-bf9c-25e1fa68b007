<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化渐变画笔测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .canvas-container {
            text-align: center;
            margin-bottom: 20px;
        }
        
        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: crosshair;
            background: white;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }
        
        .control-group label {
            font-size: 12px;
            color: #666;
            font-weight: bold;
        }
        
        .control-group input {
            width: 80px;
        }
        
        .buttons {
            text-align: center;
            margin-top: 20px;
        }
        
        button {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .info {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>优化渐变画笔测试</h1>
        
        <div class="canvas-container">
            <canvas id="canvas" width="800" height="600"></canvas>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>画笔大小</label>
                <input type="range" id="brushSize" min="5" max="50" value="20">
                <span id="brushSizeValue">20</span>
            </div>
            
            <div class="control-group">
                <label>颜色变化速度</label>
                <input type="range" id="colorSpeed" min="0" max="100" value="40">
                <span id="colorSpeedValue">0.4</span>
            </div>

            <div class="control-group">
                <label>颜色随机性</label>
                <input type="range" id="colorRandomness" min="0" max="100" value="40">
                <span id="colorRandomnessValue">0.4</span>
            </div>
        </div>
        
        <div class="buttons">
            <button onclick="clearCanvas()">清空画布</button>
            <button onclick="testDraw()">自动测试绘制</button>
        </div>
        
        <div class="info">
            <p>拖拽鼠标在画布上绘制，体验优化后的渐变效果</p>
            <p>新特性：随机颜色生成、降低颜色更新频率、更平缓的颜色过渡</p>
        </div>
    </div>

    <script>
        // 简化的渐变画笔实现
        class OptimizedGradientBrush {
            constructor(canvas) {
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
                this.isDrawing = false;
                this.colorProgress = 0;
                this.brushSize = 20;
                this.colorSpeed = 0.4;
                this.colorRandomness = 0.4;
                this.lastX = 0;
                this.lastY = 0;
            }
            
            // HSL转RGB
            hslToRgb(h, s, l) {
                h = h / 360;
                const c = (1 - Math.abs(2 * l - 1)) * s;
                const x = c * (1 - Math.abs((h * 6) % 2 - 1));
                const m = l - c / 2;
                
                let r = 0, g = 0, b = 0;
                
                if (h >= 0 && h < 1/6) {
                    r = c; g = x; b = 0;
                } else if (h >= 1/6 && h < 2/6) {
                    r = x; g = c; b = 0;
                } else if (h >= 2/6 && h < 3/6) {
                    r = 0; g = c; b = x;
                } else if (h >= 3/6 && h < 4/6) {
                    r = 0; g = x; b = c;
                } else if (h >= 4/6 && h < 5/6) {
                    r = x; g = 0; b = c;
                } else if (h >= 5/6 && h < 1) {
                    r = c; g = 0; b = x;
                }
                
                return {
                    r: Math.round((r + m) * 255),
                    g: Math.round((g + m) * 255),
                    b: Math.round((b + m) * 255)
                };
            }
            
            // 计算当前颜色（降低频率版本）
            calculateCurrentColor(progress) {
                const timeBasedRandom = Math.sin(Date.now() * 0.001) * 0.5 + 0.5;
                const progressBasedRandom = Math.sin(progress * Math.PI * 2) * 0.5 + 0.5;

                const randomFactor = (timeBasedRandom + progressBasedRandom) * 0.3;
                const moderateRandomness = this.colorRandomness * 1.5;

                const hue = (progress + randomFactor * moderateRandomness) * 360;
                const saturation = 0.85 + Math.sin(progress * Math.PI * 1.5) * 0.15;
                const lightness = 0.55 + Math.sin(progress * Math.PI * 1.2) * 0.15;

                return this.hslToRgb(hue % 360, saturation, lightness);
            }
            
            // 更新颜色进度（降低频率版本）
            updateColorProgress(distance) {
                const speedMultiplier = 0.8;
                const increment = (distance / 150) * this.colorSpeed * speedMultiplier;
                this.colorProgress += increment;

                if (this.colorProgress > 1) {
                    this.colorProgress -= 1;
                }
            }
            
            // 绘制渐变线段
            drawGradientSegment(x1, y1, x2, y2, color1, color2) {
                const gradient = this.ctx.createLinearGradient(x1, y1, x2, y2);
                gradient.addColorStop(0, `rgb(${color1.r}, ${color1.g}, ${color1.b})`);
                gradient.addColorStop(1, `rgb(${color2.r}, ${color2.g}, ${color2.b})`);
                
                this.ctx.save();
                this.ctx.strokeStyle = gradient;
                this.ctx.lineWidth = this.brushSize;
                this.ctx.lineCap = 'round';
                this.ctx.lineJoin = 'round';
                
                this.ctx.beginPath();
                this.ctx.moveTo(x1, y1);
                this.ctx.lineTo(x2, y2);
                this.ctx.stroke();
                this.ctx.restore();
            }
            
            startDrawing(x, y) {
                this.isDrawing = true;
                this.lastX = x;
                this.lastY = y;
                
                // 绘制起始点
                const color = this.calculateCurrentColor(this.colorProgress);
                this.ctx.save();
                this.ctx.fillStyle = `rgb(${color.r}, ${color.g}, ${color.b})`;
                this.ctx.beginPath();
                this.ctx.arc(x, y, this.brushSize / 2, 0, Math.PI * 2);
                this.ctx.fill();
                this.ctx.restore();
            }
            
            draw(x, y) {
                if (!this.isDrawing) return;
                
                const distance = Math.sqrt((x - this.lastX) ** 2 + (y - this.lastY) ** 2);
                this.updateColorProgress(distance);
                
                const color1 = this.calculateCurrentColor(this.colorProgress - 0.1);
                const color2 = this.calculateCurrentColor(this.colorProgress);
                
                this.drawGradientSegment(this.lastX, this.lastY, x, y, color1, color2);
                
                this.lastX = x;
                this.lastY = y;
            }
            
            stopDrawing() {
                this.isDrawing = false;
            }
        }
        
        // 初始化
        const canvas = document.getElementById('canvas');
        const brush = new OptimizedGradientBrush(canvas);
        
        // 控件更新
        document.getElementById('brushSize').addEventListener('input', (e) => {
            brush.brushSize = parseInt(e.target.value);
            document.getElementById('brushSizeValue').textContent = e.target.value;
        });
        
        document.getElementById('colorSpeed').addEventListener('input', (e) => {
            brush.colorSpeed = parseInt(e.target.value) / 100;
            document.getElementById('colorSpeedValue').textContent = (parseInt(e.target.value) / 100).toFixed(1);
        });
        
        document.getElementById('colorRandomness').addEventListener('input', (e) => {
            brush.colorRandomness = parseInt(e.target.value) / 100;
            document.getElementById('colorRandomnessValue').textContent = (parseInt(e.target.value) / 100).toFixed(1);
        });
        
        // 鼠标事件
        canvas.addEventListener('mousedown', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            brush.startDrawing(x, y);
        });
        
        canvas.addEventListener('mousemove', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            brush.draw(x, y);
        });
        
        canvas.addEventListener('mouseup', () => {
            brush.stopDrawing();
        });
        
        canvas.addEventListener('mouseleave', () => {
            brush.stopDrawing();
        });
        
        // 清空画布
        function clearCanvas() {
            brush.ctx.clearRect(0, 0, canvas.width, canvas.height);
            brush.colorProgress = 0;
        }
        
        // 自动测试绘制
        function testDraw() {
            clearCanvas();
            
            // 绘制螺旋形状来展示渐变效果
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            let angle = 0;
            let radius = 10;
            
            brush.startDrawing(centerX + radius, centerY);
            
            const drawStep = () => {
                if (angle < Math.PI * 6) {
                    angle += 0.1;
                    radius += 1.5;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    brush.draw(x, y);
                    setTimeout(drawStep, 20);
                } else {
                    brush.stopDrawing();
                }
            };
            
            setTimeout(drawStep, 100);
        }
    </script>
</body>
</html>
